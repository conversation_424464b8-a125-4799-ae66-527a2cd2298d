import { create } from "zustand";
import { devtools } from "zustand/middleware";

import { CommonApi } from "../../../api/common.api";
import { authAxios } from "../../config/axios-instance";
import { ShopInfoDto } from "../../models/common/shop-data.model";

// Store state interface
interface RootState {
  isLoading: boolean;
  shopInfo: ShopInfoDto | null;
  error: boolean;
}

// Store actions interface
interface RootActions {
  setLoading: (loading: boolean) => void;
  setShopInfo: (shopInfo: ShopInfoDto | null) => void;
  setError: (error: boolean) => void;
  reset: () => void;
  initializeRoot: () => Promise<void>;
}

// Combined store interface
type RootStore = RootState & RootActions;

// Initial state
const initialState: RootState = {
  isLoading: true,
  shopInfo: null,
  error: false
};

// Create the Zustand store
export const rootStore = create<RootStore>()(
  devtools(
    (set, get) => ({
      // State
      ...initialState,

      // Actions
      setLoading: (loading: boolean) => set({ isLoading: loading }, false, "setLoading"),

      setShopInfo: (shopInfo: ShopInfoDto | null) => set({ shopInfo }, false, "setShopInfo"),

      setError: (error: boolean) => set({ error }, false, "setError"),

      reset: () => set(initialState, false, "reset"),

      initializeRoot: async () => {
        const { setLoading, setError, setShopInfo } = get();

        try {
          setLoading(true);
          setError(false);

          const urlParams = new URLSearchParams(window.location.search);
          const instance = urlParams.get("instance");

          if (!instance) {
            setError(true);
            return;
          }

          const payload = instance.split(".")[1];
          const decoded = JSON.parse(atob(payload));

          if (!decoded?.instanceId) {
            setError(true);
            return;
          }

          // Fetch shop info
          const shopData = await CommonApi.GetShop(decoded.instanceId);

          if (shopData.result) {
            setShopInfo(shopData.result);
            // Set authorization header for future requests
            authAxios.defaults.headers.Authorization = `Bearer ${shopData.result.token}`;
          } else {
            setError(true);
          }
        } catch (error) {
          console.error("Failed to initialize root:", error);
          setError(true);
        } finally {
          setLoading(false);
        }
      }
    }),
    {
      name: "root-store" // DevTools name
    }
  )
);
